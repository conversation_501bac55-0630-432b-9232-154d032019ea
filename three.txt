请使用 three.js 实现一个逼真的"大象牙膏"化学实验3D演示。所有代码（包括HTML, CSS, JavaScript）都必须封装在一个独立的HTML文件中。

**场景设置：**
1. **平面：** 创建一个尺寸为1000*1000的灰色、光滑的水平平面它能接收阴影。
2. **三角烧瓶：**
* **形状：** 在平面中心放置一个透明的玻璃三角烧瓶。烧瓶应具有清晰的轮廓：圆柱形的颈部、逐渐变宽的圆锥形瓶身和扁平的底部。请勿使用简单的圆锥体代替。
* **材质：** 烧瓶材质应为高度透明的玻璃，具有适当的高透射率，较低的粗糙度，以及正确的折射率来模拟玻璃。设置 ```{ color: 0xffffff, transparent: true, opacity: 0.9, roughness: 0.95, metalness: 0.35, clearcoat: 1.0, clearcoatRoughness: 0.03, transmission: 0.95, ior: 1.5, side: THREE.DoubleSid}```应能看到背景和透过液体的光线折射效果。
* **液体：** 烧瓶内预先装有约三分之一高度的荧光粉色液体。液体表面应平整，并与烧瓶内壁贴合。注意参考三角烧瓶建模的形状，理想的做法是复制三角烧瓶的部分锥形的形状，不要让液体建模溢出三角烧瓶。
* **注意：** 三角烧瓶的建模整个模型的方向朝向向上。

**"大象牙膏"喷发效果模拟：**
1. **触发：** 页面在footer的位置提供一个按钮点击后开始喷发。
2. **泡沫形态与质感：**
* 喷射出的泡沫应由**大量微小、半透明、能够相互融合的粒子**组成，模拟真实泡沫的**稠密和蓬松质感**，避免看起来像孤立的小球。颜色为荧光粉色，可以带有一些亮度变化以增加层次感。
* 考虑使用一个简单的**噪点纹理**或程序化方式为泡沫粒子增加一些表面细节，使其看起来更像多孔的泡沫而非光滑球体。
3. **喷射动态与流体模拟：**
* **初期喷发：** 泡沫从烧瓶口猛烈向上喷出，形成一个持续上升的圆柱(紧密的泡沫堆积在一起形成柱状)。高度至少要有三角烧瓶的3倍高度，泡沫住直到快达到最大高度的时候才开始分散，初始喷射速度和压力较大。
* **压力衰减：** 喷射的强度（速度和产生泡沫的速率）应随时间**逐渐减弱**，导致泡沫柱的高度和喷射距离逐渐减小，模拟化学反应物耗尽的过程。
* **粒子运动：** 粒子运动轨迹应模拟**基本的流体动力学影响**，而不仅仅是简单的抛物线。例如，粒子之间应有轻微的排斥力以模拟泡沫的膨胀感，或者在喷射主流周围有随机的速度扰动。整体受重力影响。
4. **泡沫与环境交互及变形：**
* **重力与堆积：** 喷出的泡沫粒子受重力影响下落。当泡沫颗粒接触平面或烧瓶外壁时，它们应该**模拟受压变形的效果**，例如在垂直方向上被压扁（减少Y轴缩放），并在水平方向上略微扩展（增加X和Z轴缩放）。
* **堆积形态：** 落在平面和烧瓶上的泡沫应该能逐渐**堆积起来，形成一定厚度的覆盖层**，而不是消失。堆积的泡沫也应有类似的变形和融合效果。
* **泡沫固定: ** 泡沫在落到物体表面后，略微滑行一段距离便停止移动。
5. **液体减少：**
随着喷发，三角烧瓶内的液体液面逐渐下降，用来演示液体减少的效果。注意不是液体整体缩小，而是液体的顶部的面逐渐下降的效果。

**光照与渲染：**
1. **光源：**
* 设置一个主光源，以产生清晰的阴影。
* 添加一个环境光以提亮场景暗部，确保没有纯黑区域。
2. **阴影：** 启用渲染器的阴影贴图 。平面应能接收阴影，烧瓶和喷出的泡沫应能投射阴影。
3. **反射与折射：** 烧瓶的材质应能展示出环境的微弱反射和光线透过液体及玻璃的折射。

**摄像机与控制：**
* 摄像机应从斜45度角俯视场景中心（三角烧瓶所在位置），确保能清晰观察到整个喷发过程和最终堆积效果。
* **必须使用正确的OrbitControls初始化方式：** `const controls = new OrbitControls(camera, renderer.domElement);` （注意：不是 `new THREE.OrbitControls`）
